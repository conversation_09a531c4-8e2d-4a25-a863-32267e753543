import { useState, useCallback } from 'react';
import { useBoolean } from 'src/hooks/use-boolean';
import {
  useTemplatesTeamsApi,
  TemplateTeam,
  CreateTemplateTeamRequest,
} from 'src/services/api/use-templates-teams-api';

// Form values type for team creation/editing
export interface TeamFormValues {
  name: string;
  description: string;
  type: 'AUTO' | 'MANUAL';
  categoryId: number;
  model: 'GPT_4O_MINI' | 'GPT_4O' | 'CLAUDE_3_7_SONNET' | 'GEMINI_2_0_FLASH' | 'GEMINI_1_5_FLASH';
  templatesIds: number[];
}

// Export the TemplateTeam type for use in components
export type { TemplateTeam };

export function useTeamsView() {
  const [selectedTeam, setSelectedTeam] = useState<TemplateTeam | null>(null);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [selectedId, setSelectedId] = useState<number | null>(null);

  // Form dialog
  const formDialog = useBoolean();

  // Get templates-teams data from API
  const { useGetTemplatesTeams, useDeleteTemplateTeam } = useTemplatesTeamsApi();
  const { data: teamsResponse, isLoading, isError, refetch } = useGetTemplatesTeams();
  const { mutate: deleteTeam, isPending } = useDeleteTemplateTeam();

  const teams = teamsResponse?.templatesTeams || [];
  const loading = isLoading;
  const error = isError ? 'Failed to load teams' : null;

  // Handle opening the form dialog for adding a new team
  const handleAddTeam = useCallback(() => {
    setSelectedTeam(null);
    formDialog.onTrue();
  }, [formDialog]);

  // Handle opening the form dialog for editing a team
  const handleEditTeam = useCallback(
    (id: number) => {
      const team = teams.find((t) => t.id === id);
      if (team) {
        setSelectedTeam(team);
        formDialog.onTrue();
      }
    },
    [teams, formDialog]
  );

  // Handle viewing a team
  const handleViewTeam = useCallback((id: number) => {
    // Implement view functionality here
    console.log(`Viewing team with ID: ${id}`);
  }, []);

  // Handle opening the confirm dialog for deleting a team
  const handleOpenConfirmDialog = useCallback((id: number) => {
    setSelectedId(id);
    setOpenConfirmDialog(true);
  }, []);

  // Handle closing the confirm dialog
  const handleCloseConfirmDialog = useCallback(() => {
    setOpenConfirmDialog(false);
    setSelectedId(null);
  }, []);

  // Handle deleting a team
  const handleDeleteTeam = useCallback(() => {
    if (selectedId) {
      deleteTeam(selectedId, {
        onSuccess: () => {
          refetch();
          handleCloseConfirmDialog();
        },
        onError: (error) => {
          console.error('Failed to delete team:', error);
          handleCloseConfirmDialog();
        },
      });
    }
  }, [selectedId, deleteTeam, refetch, handleCloseConfirmDialog]);

  // Handle form submission
  const handleFormSubmit = useCallback(
    (data: TeamFormValues) => {
      // Form submission is handled in the form itself
      formDialog.onFalse();
      refetch(); // Refetch data after submission
    },
    [formDialog, refetch]
  );

  return {
    // State
    teams,
    selectedTeam,
    openConfirmDialog,
    selectedId,
    formDialog,
    loading,
    error,
    isPending,

    // Handlers
    handleAddTeam,
    handleEditTeam,
    handleViewTeam,
    handleOpenConfirmDialog,
    handleCloseConfirmDialog,
    handleDeleteTeam,
    handleFormSubmit,

    // API
    refetch,
  };
}
