import { useState, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '@mui/material';
import { useTemplatesApi, Template } from 'src/services/api/use-templates-api';
import { useCategoriesApi } from 'src/services/api/use-categories-api';
import { useToolsApi } from 'src/services/api/use-tools-api';
import { paths } from 'src/routes/paths';
import {
  agentFormSchema,
  AgentFormValues,
  DEFAULT_FORM_VALUES,
  FORM_STEPS,
  LLM_MODEL_OPTIONS,
  CHANNEL_OPTIONS,
  AGENT_TYPE_OPTIONS,
  AGENT_STATUS_OPTIONS,
  TYPE_OPTIONS,
  STATUS_OPTIONS,
} from '../config/agent-form-config';
import {
  templateToFormValues,
  formValuesToSubmission,
  getDefaultFormValues,
  getStepCompletionStatus,
  toggleArrayItem,
  filterItemsBySearch,
  filterChannelsBySearch,
  formatCategoryOptions,
  getStepValidationFields,
} from '../utils/form-utils';

// ----------------------------------------------------------------------

interface UseAgentFormProps {
  agent: Template | null;
}

export function useAgentForm({ agent }: UseAgentFormProps) {
  const theme = useTheme();
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(0);

  // API hooks
  const { useCreateTemplate, useUpdateTemplate } = useTemplatesApi();
  const { useGetCategories } = useCategoriesApi();
  const { useGetTools } = useToolsApi();

  const { mutate: createTemplate, isPending: isCreating } = useCreateTemplate();
  const { mutate: updateTemplate, isPending: isUpdating } = useUpdateTemplate(agent?.id || 0);
  const { data: categoriesResponse } = useGetCategories();
  const { data: toolsResponse } = useGetTools();

  const categories = categoriesResponse?.categories || [];
  const tools = toolsResponse?.tools || [];
  const isLoading = isCreating || isUpdating;
  const isEditing = Boolean(agent);

  // Search state
  const [toolSearchQuery, setToolSearchQuery] = useState('');
  const [categorySearchQuery, setCategorySearchQuery] = useState('');
  const [channelSearchQuery, setChannelSearchQuery] = useState('');

  // Form setup
  const methods = useForm<AgentFormValues>({
    mode: 'onChange',
    resolver: zodResolver(agentFormSchema),
    defaultValues: getDefaultFormValues(),
  });

  const {
    handleSubmit,
    trigger,
    reset,
    watch,
    setValue,
    formState: { isSubmitting, errors },
  } = methods;

  // Watch all form values for step completion checking
  const formValues = watch();

  // Reset form when agent changes
  useEffect(() => {
    if (agent) {
      // Use utility function to transform template to form values
      const formValues = templateToFormValues(agent);
      reset(formValues);
    } else {
      // Reset to default values for creating new agent
      reset(getDefaultFormValues());
    }
  }, [agent, reset]);

  // Search handlers
  const handleToolSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setToolSearchQuery(event.target.value);
  }, []);

  const handleCategorySearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setCategorySearchQuery(event.target.value);
  }, []);

  const handleChannelSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setChannelSearchQuery(event.target.value);
  }, []);

  // Watch form values for selections
  const selectedTools = watch('toolsId') || [];
  const selectedChannels = watch('channels') || [];
  const selectedModel = watch('model');

  // Filter data based on search queries
  const filteredTools = filterItemsBySearch(tools, toolSearchQuery);
  const filteredCategories = formatCategoryOptions(categories, categorySearchQuery);
  const filteredChannels = filterChannelsBySearch([...CHANNEL_OPTIONS], channelSearchQuery);
  const filteredModels = filterChannelsBySearch([...LLM_MODEL_OPTIONS], channelSearchQuery);

  // Selection handlers
  const handleChannelToggle = useCallback((channelValue: string) => {
    const currentChannels = [...selectedChannels];
    const newChannels = toggleArrayItem(currentChannels, channelValue);
    setValue('channels', newChannels);
  }, [selectedChannels, setValue]);

  const handleModelSelect = useCallback((
    modelValue: 'GPT_4O_MINI' | 'GPT_4O' | 'CLAUDE_3_7_SONNET' | 'GEMINI_2_0_FLASH' | 'GEMINI_1_5_FLASH'
  ) => {
    setValue('model', modelValue);
  }, [setValue]);

  // Navigation handlers
  const handleNext = useCallback(async () => {
    const currentStep = FORM_STEPS[activeStep];
    if (!currentStep) return;

    // Validate current step fields
    const isStepValid = await trigger(currentStep.fields as any);

    if (isStepValid) {
      if (activeStep < FORM_STEPS.length - 1) {
        setActiveStep((prev) => prev + 1);
      }
    }
  }, [activeStep, trigger]);

  const handleBack = useCallback(() => {
    if (activeStep > 0) {
      setActiveStep((prev) => prev - 1);
    }
  }, [activeStep]);

  const handleStepClick = useCallback(
    async (stepIndex: number) => {
      // Validate all previous steps before allowing navigation
      let canNavigate = true;

      for (let i = 0; i < stepIndex; i += 1) {
        const step = FORM_STEPS[i];
        // eslint-disable-next-line no-await-in-loop
        const isStepValid = await trigger(step.fields as any);
        if (!isStepValid) {
          canNavigate = false;
          break;
        }
      }

      if (canNavigate) {
        setActiveStep(stepIndex);
      }
    },
    [trigger]
  );

  // Form submission
  const onFormSubmit = handleSubmit(async (data: AgentFormValues) => {
    try {
      // Use utility function to transform form values to submission format
      const submissionData = formValuesToSubmission(data);
      if (submissionData?.toolsId?.length === 0) {
        delete submissionData?.toolsId;
      }

      if (isEditing) {
        // Update existing template
        await new Promise((resolve, reject) => {
          updateTemplate(submissionData, {
            onSuccess: resolve,
            onError: reject,
          });
        });
      } else {
        // Create new template
        await new Promise((resolve, reject) => {
          createTemplate(submissionData, {
            onSuccess: resolve,
            onError: reject,
          });
        });
      }

      // Navigate back to agents list
      navigate(paths.dashboard.agents.root);
    } catch (error) {
      console.error('Form submission error:', error);
      // Error handling could be improved with toast notifications
    }
  });

  // Helper functions
  const getCurrentStepErrors = useCallback(() => {
    const currentStep = FORM_STEPS[activeStep];
    if (!currentStep) return [];

    return currentStep.fields.filter((field) => errors[field]);
  }, [activeStep, errors]);

  const isStepCompleted = useCallback(
    (stepIndex: number) => {
      const step = FORM_STEPS[stepIndex];
      if (!step) return false;

      const { isCompleted } = getStepCompletionStatus(step.fields, errors, formValues);
      return isCompleted;
    },
    [errors, formValues]
  );

  const canProceedToNext = useCallback(() => {
    return getCurrentStepErrors().length === 0;
  }, [getCurrentStepErrors]);

  return {
    // Form state
    methods,
    activeStep,
    isLoading,
    isSubmitting,
    isEditing,
    theme,

    // Navigation
    handleNext,
    handleBack,
    handleStepClick,

    // Form submission
    onFormSubmit,

    // Helper functions
    getCurrentStepErrors,
    isStepCompleted,
    canProceedToNext,

    // Data and filtering
    categories: filteredCategories,
    tools: filteredTools,
    availableTools: filteredTools,
    options: filteredCategories,

    // Search functionality
    toolSearchQuery,
    categorySearchQuery,
    channelSearchQuery,
    handleToolSearchChange,
    handleCategorySearchChange,
    handleChannelSearchChange,

    // Selection state
    selectedTools,
    selectedChannels,
    selectedModel,

    // Selection handlers
    handleChannelToggle,
    handleModelSelect,

    // Filtered data
    LLM_MODEL: filteredModels,

    // Constants and options
    steps: FORM_STEPS,
    totalSteps: FORM_STEPS.length,
    isLastStep: activeStep === FORM_STEPS.length - 1,
    isFirstStep: activeStep === 0,
    TYPE_OPTIONS,
    STATUS_OPTIONS,
  };
}

export default useAgentForm;
