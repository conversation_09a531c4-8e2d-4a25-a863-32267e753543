import { 
  Stepper, 
  Step, 
  StepLabel, 
  <PERSON><PERSON><PERSON><PERSON>,
  Box, 
  Typography,
  useTheme,
  alpha
} from '@mui/material';
import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

interface FormStep {
  id: string;
  label: string;
  description?: string;
  icon?: string;
}

interface FormStepperProps {
  steps: readonly FormStep[];
  activeStep: number;
  onStepClick?: (stepIndex: number) => void;
  isStepCompleted?: (stepIndex: number) => boolean;
  isStepError?: (stepIndex: number) => boolean;
  orientation?: 'horizontal' | 'vertical';
  alternativeLabel?: boolean;
}

export function FormStepper({
  steps,
  activeStep,
  onStepClick,
  isStepCompleted,
  isStepError,
  orientation = 'horizontal',
  alternativeLabel = true,
}: FormStepperProps) {
  const theme = useTheme();
  
  const getStepIcon = (stepIndex: number, step: FormStep) => {
    const isActive = stepIndex === activeStep;
    const isCompleted = isStepCompleted?.(stepIndex) ?? false;
    const hasError = isStepError?.(stepIndex) ?? false;

    let iconColor = theme.palette.text.disabled;
    let bgColor = 'transparent';
    let borderColor = theme.palette.divider;

    if (hasError) {
      iconColor = theme.palette.error.main;
      borderColor = theme.palette.error.main;
    } else if (isCompleted) {
      iconColor = theme.palette.common.white;
      bgColor = theme.palette.success.main;
      borderColor = theme.palette.success.main;
    } else if (isActive) {
      iconColor = theme.palette.common.white;
      bgColor = theme.palette.primary.main;
      borderColor = theme.palette.primary.main;
    }
    
    return (
      <Box
        sx={{
          width: 40,
          height: 40,
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: '2px solid',
          borderColor,
          bgcolor: bgColor,
          color: iconColor,
          transition: 'all 0.2s ease-in-out',
        }}
      >
        {isCompleted ? (
          <Iconify icon="eva:checkmark-fill" width={20} />
        ) : hasError ? (
          <Iconify icon="eva:close-fill" width={20} />
        ) : step.icon ? (
          <Iconify icon={step.icon} width={20} />
        ) : (
          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
            {stepIndex + 1}
          </Typography>
        )}
      </Box>
    );
  };
  
  const getStepLabelStyles = (stepIndex: number) => {
    const isActive = stepIndex === activeStep;
    const isCompleted = isStepCompleted?.(stepIndex) ?? false;
    const hasError = isStepError?.(stepIndex) ?? false;

    return {
      '& .MuiStepLabel-label': {
        fontWeight: isActive ? 600 : 500,
        color: hasError
          ? theme.palette.error.main
          : isActive || isCompleted
            ? theme.palette.text.primary
            : theme.palette.text.secondary,
      },
      '& .MuiStepLabel-optional': {
        color: theme.palette.text.secondary,
        fontSize: '0.75rem',
      },
      ...(isActive && {
        bgcolor: alpha(theme.palette.primary.main, 0.08),
        borderRadius: 2,
        p: 1,
        mx: -1,
      }),
    };
  };
  
  return (
    <Stepper
      activeStep={activeStep}
      orientation={orientation}
      alternativeLabel={alternativeLabel}
      sx={{
        mb: 4,
        '& .MuiStepConnector-line': {
          borderColor: theme.palette.divider,
          borderTopWidth: 2,
        },
        '& .MuiStepConnector-root.Mui-active .MuiStepConnector-line': {
          borderColor: theme.palette.primary.main,
        },
        '& .MuiStepConnector-root.Mui-completed .MuiStepConnector-line': {
          borderColor: theme.palette.success.main,
        },
      }}
    >
      {steps.map((step, index) => (
        <Step key={step.id}>
          {onStepClick ? (
            <StepButton
              onClick={() => onStepClick(index)}
              sx={{
                '& .MuiStepLabel-root': getStepLabelStyles(index),
              }}
            >
              <StepLabel
                StepIconComponent={() => getStepIcon(index, step)}
                optional={
                  step.description ? (
                    <Typography variant="caption">{step.description}</Typography>
                  ) : null
                }
              >
                {step.label}
              </StepLabel>
            </StepButton>
          ) : (
            <StepLabel
              StepIconComponent={() => getStepIcon(index, step)}
              optional={
                step.description ? (
                  <Typography variant="caption">{step.description}</Typography>
                ) : null
              }
              sx={getStepLabelStyles(index)}
            >
              {step.label}
            </StepLabel>
          )}
        </Step>
      ))}
    </Stepper>
  );
}

export default FormStepper;
