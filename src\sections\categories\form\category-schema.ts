import * as z from 'zod';

export const categorySchema = z.object({
  name: z.string().min(1, 'Category name is required'),
  description: z.string().min(1, 'Description is required'),
  icon: z.string().min(1, 'Icon is required'),
  colorType: z.enum(['primary', 'secondary', 'success', 'warning', 'info', 'error', 'custom']),
  customColor: z
    .string()
    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Invalid hex color')
    .optional(),
});

export type CategoryFormValues = z.infer<typeof categorySchema>;
