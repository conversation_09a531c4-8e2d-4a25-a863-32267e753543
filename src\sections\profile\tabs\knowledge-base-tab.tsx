import { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  Grid,
  Card,
  LinearProgress,
  Chip,
  TextField,
  InputAdornment,
  IconButton,
  alpha,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { useTranslation } from 'react-i18next';
import { Iconify } from 'src/components/iconify';

import ConnectResourceDialog from '../components/connect-resource-dialog';
import DatabasesDialog from '../components/databases-dialog';
import APIsDialog from '../components/apis-dialog';
import FileUploadDialog from '../components/file-upload-dialog';
import KnowledgeBaseTable from '../components/knowledge-base-table';
import CloudPlatformsCarousel from '../components/cloud-platforms-carousel';
import useKnowledgeBaseTable from '../hooks/use-knowledge-base-table';

// ----------------------------------------------------------------------

// Mock data for cloud storage platforms
const CLOUD_PLATFORMS = [
  {
    id: 'google-drive',
    name: 'Google Drive',
    icon: '/assets/icons/platforms/ic_google_drive.svg',
    files: 561,
    color: '#FEE4DC',
    progress: 70,
  },
  {
    id: 'mega-drive',
    name: 'Mega Drive',
    icon: '/assets/icons/platforms/ic_mega.svg',
    files: 561,
    color: '#FFE8E5',
    progress: 40,
  },
  {
    id: 'dropbox',
    name: 'Dropbox',
    icon: '/assets/icons/platforms/ic_dropbox.svg',
    files: 561,
    color: '#E0F8FF',
    progress: 50,
  },
  {
    id: 'one-drive',
    name: 'One Drive',
    icon: '/assets/icons/platforms/ic_onedrive.svg',
    files: 561,
    color: '#E0F8FF',
    progress: 30,
  },
  {
    id: 'google-drive-2',
    name: 'Google Drive',
    icon: '/assets/icons/platforms/ic_google_drive.svg',
    files: 561,
    color: '#FEE4DC',
    progress: 70,
  },
  {
    id: 'mega-drive-2',
    name: 'Mega Drive',
    icon: '/assets/icons/platforms/ic_mega.svg',
    files: 561,
    color: '#FFE8E5',
    progress: 40,
  },
];

// Mock data for connection options
const CONNECTION_OPTIONS = [
  {
    id: 'connect',
    title: 'Connect',
    description: 'Connect to a new cloud platform',
    icon: 'eva:link-2-fill',
    color: '#7D40D9',
  },
  {
    id: 'databases',
    title: 'Databases',
    description: 'Connect to database',
    icon: 'eva:layers-fill',
    color: '#7D40D9',
  },
  {
    id: 'api',
    title: 'API',
    description: 'Connect to a custom API',
    icon: 'eva:code-fill',
    color: '#7D40D9',
  },
];

// Mock data for file categories
const FILE_CATEGORIES = [
  {
    id: 'documents',
    title: 'Documents',
    count: 60,
    size: '1.52 GB',
    icon: 'eva:file-text-fill',
    color: '#7D40D9',
  },
  {
    id: 'pictures',
    title: 'Pictures',
    count: 60,
    size: '1.52 GB',
    icon: 'eva:image-fill',
    color: '#7D40D9',
  },
  {
    id: 'audio',
    title: 'Audio',
    count: 60,
    size: '1.52 GB',
    icon: 'eva:music-fill',
    color: '#7D40D9',
  },
  {
    id: 'videos',
    title: 'Videos',
    count: 60,
    size: '1.52 GB',
    icon: 'eva:video-fill',
    color: '#7D40D9',
  },
  {
    id: 'downloads',
    title: 'Downloads',
    count: 60,
    size: '1.52 GB',
    icon: 'eva:download-fill',
    color: '#7D40D9',
  },
  {
    id: 'trash',
    title: 'Trash',
    count: 60,
    size: '1.52 GB',
    icon: 'eva:trash-2-fill',
    color: '#7D40D9',
  },
];

// File data is now in the useKnowledgeBaseTable hook

// ----------------------------------------------------------------------

export default function KnowledgeBaseTab() {
  const { t } = useTranslation();

  const [connectDialogOpen, setConnectDialogOpen] = useState(false);
  const [databasesDialogOpen, setDatabasesDialogOpen] = useState(false);
  const [apisDialogOpen, setApisDialogOpen] = useState(false);
  const [fileUploadDialogOpen, setFileUploadDialogOpen] = useState(false);

  const {
    searchQuery,
    setSearchQuery,
    selectedDate,
    setSelectedDate,
    selectedCategory,
    handleClearCategory,
  } = useKnowledgeBaseTable();

  const handleOpenConnectDialog = () => {
    setConnectDialogOpen(true);
  };

  const handleCloseConnectDialog = () => {
    setConnectDialogOpen(false);
  };

  const handleOpenDatabasesDialog = () => {
    setDatabasesDialogOpen(true);
  };

  const handleCloseDatabasesDialog = () => {
    setDatabasesDialogOpen(false);
  };

  const handleOpenApisDialog = () => {
    setApisDialogOpen(true);
  };

  const handleCloseApisDialog = () => {
    setApisDialogOpen(false);
  };

  const handleOpenFileUploadDialog = () => {
    setFileUploadDialogOpen(true);
  };

  const handleCloseFileUploadDialog = () => {
    setFileUploadDialogOpen(false);
  };

  return (
    <Box sx={{ width: '100%', px: { xs: 2, sm: 3, md: 4, lg: 5 }, py: 3 }}>
      {/* Connect Resource Dialog */}
      <ConnectResourceDialog open={connectDialogOpen} onClose={handleCloseConnectDialog} />

      {/* Databases Dialog */}
      <DatabasesDialog open={databasesDialogOpen} onClose={handleCloseDatabasesDialog} />

      {/* APIs Dialog */}
      <APIsDialog open={apisDialogOpen} onClose={handleCloseApisDialog} />

      {/* File Upload Dialog */}
      <FileUploadDialog open={fileUploadDialogOpen} onClose={handleCloseFileUploadDialog} />
      {/* Header Section */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          justifyContent: 'space-between',
          alignItems: { xs: 'flex-start', sm: 'center' },
          mb: 3,
          gap: { xs: 2, sm: 0 },
        }}
      >
        <Box sx={{ width: { xs: '100%', sm: 'auto' } }}>
          <Box
            sx={{
              display: 'flex',
              alignItems: { xs: 'flex-start', sm: 'center' },
              flexDirection: { xs: 'column', sm: 'row' },
              gap: { xs: 1, sm: 2 },
              mb: 1,
            }}
          >
            <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
              {t('knowledge Base')}
            </Typography>
          </Box>
          <Typography variant="body2" color="text.secondary">
            {t('description')}
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<Iconify icon="eva:upload-fill" />}
          onClick={handleOpenFileUploadDialog}
          sx={{
            bgcolor: 'primary.main',
            '&:hover': { bgcolor: 'primary.dark' },
            borderRadius: 1,
            alignSelf: { xs: 'flex-start', sm: 'center' },
            whiteSpace: 'nowrap',
            px: { xs: 2, sm: 3 },
            py: { xs: 0.75, sm: 1 },
            fontSize: { xs: '0.8125rem', sm: '0.875rem' },
          }}
        >
          {t('upload')}
        </Button>
      </Box>

      {/* Storage Progress */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="subtitle2">Cloud storage</Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
              17% (3.59 GB used)
            </Typography>
            <Typography variant="subtitle2">500 GB</Typography>
            <Button
              variant="text"
              size="small"
              sx={{ ml: 1, color: 'primary.main', fontWeight: 'bold' }}
            >
              Upgrade
            </Button>
          </Box>
        </Box>
        <LinearProgress
          variant="determinate"
          value={17}
          sx={{
            height: 8,
            borderRadius: 4,
            bgcolor: 'primary.lighter',
            '& .MuiLinearProgress-bar': {
              bgcolor: 'primary.main',
              borderRadius: 4,
            },
          }}
        />
      </Box>

      {/* Cloud Platforms */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2">Connected platforms</Typography>
        </Box>
        <CloudPlatformsCarousel platforms={CLOUD_PLATFORMS} />
      </Box>

      {/* Connection Options */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        {CONNECTION_OPTIONS.map((option) => (
          <Grid item xs={12} sm={4} key={option.id}>
            <Card
              sx={{
                p: 3,
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                border: '1px dashed #ccc',
                boxShadow: 'none',
                cursor: 'pointer',
                '&:hover': {
                  borderColor: 'primary.main',
                },
              }}
              onClick={() => {
                if (option.id === 'connect') {
                  handleOpenConnectDialog();
                } else if (option.id === 'databases') {
                  handleOpenDatabasesDialog();
                } else if (option.id === 'api') {
                  handleOpenApisDialog();
                }
              }}
            >
              <Box
                sx={{
                  width: 48,
                  height: 48,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: '50%',
                  mb: 2,
                  color: option.color,
                }}
              >
                <Iconify icon={option.icon} width={48} height={48} />
              </Box>
              <Typography variant="subtitle1" sx={{ mb: 0.5, fontWeight: 'bold' }}>
                {option.title}
              </Typography>
              <Typography variant="body2" color="text.secondary" align="center">
                {option.description}
              </Typography>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* File Categories */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        {FILE_CATEGORIES.map((category) => (
          <Grid item xs={6} sm={4} md={2} key={category.id}>
            <Card
              sx={{
                p: 2,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start',
                boxShadow: 'none',
                border: '1px solid #f0f0f0',
                cursor: 'pointer',
                '&:hover': {
                  borderColor: 'primary.main',
                },
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  mb: 1,
                }}
              >
                <Box
                  sx={{
                    width: 40,
                    height: 40,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: 1,
                    bgcolor: category.color + '20',
                    color: category.color,
                    mr: 1,
                  }}
                >
                  <Iconify icon={category.icon} width={24} height={24} />
                </Box>
                <Box>
                  <Typography variant="subtitle2">{category.title}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {category.count} files • {category.size}
                  </Typography>
                </Box>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Files Table Section */}
      <Box sx={{ mb: 3 }}>
        {/* Table Filters */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'flex-start', sm: 'center' },
            mb: 2,
            gap: { xs: 2, sm: 0 },
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="Date"
                value={new Date(selectedDate)}
                onChange={(newDate) => {
                  if (newDate) {
                    const formattedDate = new Intl.DateTimeFormat('en-US', {
                      day: 'numeric',
                      month: 'short',
                      year: 'numeric',
                    }).format(newDate);
                    setSelectedDate(formattedDate);
                  }
                }}
                slotProps={{
                  textField: {
                    size: 'small',
                    sx: {
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 1,
                        bgcolor: (theme) =>
                          theme.palette.mode === 'dark'
                            ? alpha(theme.palette.common.white, 0.08)
                            : '#f5f5f5',
                      },
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: (theme) =>
                          theme.palette.mode === 'dark'
                            ? alpha(theme.palette.common.white, 0.23)
                            : alpha(theme.palette.grey[500], 0.2),
                      },
                      '& .MuiInputLabel-root': {
                        color: (theme) =>
                          theme.palette.mode === 'dark'
                            ? theme.palette.common.white
                            : theme.palette.text.primary,
                      },
                      '& .MuiInputBase-input': {
                        color: (theme) =>
                          theme.palette.mode === 'dark'
                            ? theme.palette.common.white
                            : theme.palette.text.primary,
                      },
                      '& .MuiSvgIcon-root': {
                        color: (theme) =>
                          theme.palette.mode === 'dark'
                            ? theme.palette.common.white
                            : theme.palette.text.primary,
                      },
                    },
                  },
                  openPickerButton: {
                    size: 'small',
                  },
                }}
              />
            </LocalizationProvider>
          </Box>
          <TextField
            placeholder="Search..."
            size="small"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify
                    icon="eva:search-fill"
                    width={20}
                    sx={{
                      color: (theme) =>
                        theme.palette.mode === 'dark'
                          ? alpha(theme.palette.common.white, 0.7)
                          : 'text.disabled',
                    }}
                  />
                </InputAdornment>
              ),
            }}
            sx={{
              width: { xs: '100%', sm: '90%' },
              ml: { xs: 0, sm: '5px' },
              mt: { xs: 2, sm: 0 },
              '& .MuiOutlinedInput-root': {
                borderRadius: 1,
                bgcolor: (theme) =>
                  theme.palette.mode === 'dark'
                    ? alpha(theme.palette.common.white, 0.08)
                    : '#f5f5f5',
              },
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: (theme) =>
                  theme.palette.mode === 'dark'
                    ? alpha(theme.palette.common.white, 0.23)
                    : alpha(theme.palette.grey[500], 0.2),
              },
              '& .MuiInputBase-input': {
                color: (theme) =>
                  theme.palette.mode === 'dark'
                    ? theme.palette.common.white
                    : theme.palette.text.primary,
                '&::placeholder': {
                  color: (theme) =>
                    theme.palette.mode === 'dark'
                      ? alpha(theme.palette.common.white, 0.5)
                      : theme.palette.text.disabled,
                  opacity: 1,
                },
              },
            }}
          />
          <IconButton>
            <Iconify icon="eva:more-vertical-fill" />
          </IconButton>
        </Box>

        {/* Results Count and Category Filter */}
        <Typography variant="body2" sx={{ mr: 2 }}>
          30 {t('results Found')}
        </Typography>
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            alignItems: { xs: 'flex-start', sm: 'center' },
            mb: 2,
            gap: { xs: 1, sm: 0 },
          }}
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              flexWrap: 'wrap',
              gap: 1,
            }}
          >
            <Typography
              variant="body2"
              sx={{
                color: (theme) =>
                  theme.palette.mode === 'dark'
                    ? theme.palette.common.white
                    : theme.palette.text.primary,
              }}
            >
              {t('category')}:
            </Typography>
            {selectedCategory && (
              <Chip
                label={selectedCategory}
                onDelete={handleClearCategory}
                size="small"
                sx={{
                  bgcolor: (theme) =>
                    theme.palette.mode === 'dark'
                      ? alpha(theme.palette.common.white, 0.08)
                      : '#f5f5f5',
                  color: (theme) =>
                    theme.palette.mode === 'dark' ? theme.palette.common.white : 'black',
                  '& .MuiChip-deleteIcon': {
                    color: (theme) =>
                      theme.palette.mode === 'dark' ? theme.palette.common.white : 'black',
                    '&:hover': {
                      color: (theme) =>
                        theme.palette.mode === 'dark'
                          ? alpha(theme.palette.common.white, 0.7)
                          : alpha(theme.palette.common.black, 0.7),
                    },
                  },
                }}
              />
            )}

            <Box
              sx={{
                display: 'flex',
                gap: '5px',
                alignItems: 'center',
                ml: { xs: 0, sm: '10px' },
                cursor: 'pointer',
              }}
              onClick={handleClearCategory}
            >
              <Iconify
                width="20px"
                height="20px"
                icon="material-symbols:delete-outline"
                sx={{
                  color: (theme) => (theme.palette.mode === 'dark' ? '#FF7A59' : '#FF5630'),
                }}
              />
              <Typography
                color="error"
                sx={{
                  fontSize: '0.875rem',
                  fontWeight: 'medium',
                }}
              >
                clear
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Files Table */}
        <KnowledgeBaseTable />
      </Box>
    </Box>
  );
}
