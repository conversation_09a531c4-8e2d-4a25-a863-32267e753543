import { useState, useCallback, useMemo } from 'react';
import { Stack, Typography, Box } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useTemplatesApi, Template } from 'src/services/api/use-templates-api';
import { AppTablePropsType } from 'src/components/table';
import LongMenu from 'src/components/long-menu';
import useTable from 'src/components/table/use-table';
import { paths } from 'src/routes/paths';

// Define tab types
export type TemplateTab = 'admin' | 'user';

export interface TabConfig {
  value: TemplateTab;
  label: string;
}

export const useAgentView = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [selectedId, setSelectedId] = useState<number | null>(null);

  // Tab state
  const [activeTab, setActiveTab] = useState<TemplateTab>('admin');

  // Filtering states
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [toolFilter, setToolFilter] = useState('all');
  const [modelFilter, setModelFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');

  const table = useTable({
    defaultOrderBy: 'name',
  });

  // Get templates data from API with visibility filtering
  const {
    useGetTemplates,
    useDeleteTemplate,
    useUpdateTemplate,
    useUpdatePublishStatus,
  } = useTemplatesApi();

  // Use visibility filter based on active tab
  const visibility = activeTab === 'admin' ? 'PUBLIC' : 'PRIVATE';

  const {
    data: templatesResponse,
    isLoading,
    isError,
    refetch,
  } = useGetTemplates({ visibility });

  const { mutate: deleteTemplate, isPending } = useDeleteTemplate();

  // State for tracking which template is being updated for publish status
  const [updatingPublishId, setUpdatingPublishId] = useState<number | null>(null);

  // Create publish status mutation
  const { mutate: updatePublishStatus } = useUpdatePublishStatus(updatingPublishId || 0, () => {
    refetch();
    setUpdatingPublishId(null);
  });

  // Get current data
  const templates = templatesResponse?.templates || [];
  const loading = isLoading;
  const error = isError ? 'Failed to load templates' : null;

  // Handle tab change
  const handleTabChange = useCallback((_event: React.SyntheticEvent, newValue: TemplateTab) => {
    setActiveTab(newValue);
  }, []);

  // Handle search query change
  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  }, []);

  // Handle category filter change
  const handleCategoryChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setCategoryFilter(event.target.value);
  }, []);

  // Handle type filter change
  const handleTypeChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setTypeFilter(event.target.value);
  }, []);

  // Handle tool filter change
  const handleToolChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setToolFilter(event.target.value);
  }, []);

  // Handle model filter change
  const handleModelChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setModelFilter(event.target.value);
  }, []);

  // Handle status filter change
  const handleStatusChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setStatusFilter(event.target.value);
  }, []);

  // Handle editing a template
  const handleEditTemplate = useCallback(
    (id: number) => {
      // Navigate to edit page with template ID
      navigate(paths.dashboard.agents.edit.replace(':id', id.toString()));
    },
    [navigate]
  );

  // Handle opening the confirm dialog for deleting templates
  const handleOpenConfirmDialog = useCallback((id: number) => {
    setSelectedId(id);
    setOpenConfirmDialog(true);
  }, []);

  // Handle closing the confirm dialog
  const handleCloseConfirmDialog = useCallback(() => {
    setOpenConfirmDialog(false);
    setSelectedId(null);
  }, []);

  // Handle confirming delete
  const handleConfirmDelete = useCallback(() => {
    if (selectedId) {
      deleteTemplate(selectedId, {
        onSuccess: () => {
          refetch();
          handleCloseConfirmDialog();
        },
        onError: (error) => {
          console.error('Failed to delete template:', error);
          handleCloseConfirmDialog();
        },
      });
    }
  }, [selectedId, deleteTemplate, refetch, handleCloseConfirmDialog]);

  // Handle publish status change (for admin templates)
  const handlePublishStatus = useCallback(
    (templateId: number, status: 'APPROVED' | 'REJECTED') => {
      setUpdatingPublishId(templateId);
      updatePublishStatus({ status });
    },
    [updatePublishStatus]
  );

  // Create a state to track which template is being updated
  const [updatingTemplateId, setUpdatingTemplateId] = useState<number | null>(null);

  // Create update mutation for status toggle
  const { mutate: updateTemplateStatus } = useUpdateTemplate(updatingTemplateId || 0, () => {
    refetch(); // Refresh the table data after successful update
    setUpdatingTemplateId(null); // Reset the updating template ID
  });

  // Handle toggling template status (Active/Disabled)
  const handleToggleStatus = useCallback(
    (template: Template) => {
      const newStatus: 'ACTIVE' | 'DISABLED' = template.status === 'ACTIVE' ? 'DISABLED' : 'ACTIVE';

      // Create update payload with only the status changed
      const updatePayload = {
        name: template.name,
        description: template.description,
        systemMessage: template.systemMessage,
        type: template.type,
        categoryId: template.categoryId,
        model: template.model,
        toolsId: template.templateTools?.map((tt) => tt.tool.id) || [],
        status: newStatus,
      };

      // Set the template ID being updated and trigger the mutation
      setUpdatingTemplateId(template.id);
      updateTemplateStatus(updatePayload);
    },
    [updateTemplateStatus]
  );

  // Filter templates based on search query and category filter
  const filteredTemplates = useMemo(() => {
    return templates.filter((template) => {
      // Filter by search query
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesSearch =
          template.name.toLowerCase().includes(query) ||
          template.description.toLowerCase().includes(query) ||
          template.category.name.toLowerCase().includes(query);
        if (!matchesSearch) return false;
      }

      // Filter by category
      if (categoryFilter !== 'all' && template.categoryId.toString() !== categoryFilter) {
        return false;
      }

      // Filter by type
      if (typeFilter !== 'all' && template.type !== typeFilter) {
        return false;
      }

      // Filter by tool
      if (toolFilter !== 'all') {
        const hasTool = template.templateTools?.some((t) => t.tool.name === toolFilter) || false;
        if (!hasTool) return false;
      }

      // Filter by model
      if (modelFilter !== 'all' && template.model !== modelFilter) {
        return false;
      }

      // Filter by status
      if (statusFilter !== 'all' && template.status !== statusFilter) {
        return false;
      }

      return true;
    });
  }, [templates, searchQuery, categoryFilter, typeFilter, toolFilter, modelFilter, statusFilter]);

  // Calculate category options with counts
  const categoryOptions = useMemo(() => {
    const categoryCounts: Record<string, number> = {};

    // Count templates by category
    filteredTemplates.forEach((template) => {
      const categoryId = template.categoryId.toString();
      categoryCounts[categoryId] = (categoryCounts[categoryId] || 0) + 1;
    });

    // Get unique categories from templates
    const uniqueCategories = templates.reduce(
      (acc, template) => {
        const categoryId = template.categoryId.toString();
        if (!acc.find((cat) => cat.value === categoryId)) {
          acc.push({
            value: categoryId,
            label: template.category.name,
            count: categoryCounts[categoryId] || 0,
          });
        }
        return acc;
      },
      [] as { value: string; label: string; count: number }[]
    );

    return [
      { value: 'all', label: 'Categories', count: templates.length },
      ...uniqueCategories.sort((a, b) => a.label.localeCompare(b.label)),
    ];
  }, [templates, filteredTemplates]);

  // ---- FILTER OPTIONS ----

  // Calculate type options
  const typeOptions = useMemo(() => {
    const types = templates.map((t) => t.type);
    const uniqueTypes = Array.from(new Set(types));
    return [
      { value: 'all', label: 'Types' },
      ...uniqueTypes.map((type) => ({
        value: type,
        label: type,
      })),
    ];
  }, [templates]);

  // Calculate tool options
  const toolOptions = useMemo(() => {
    const tools = templates.flatMap((t) => t.templateTools?.map((tt) => tt.tool.name) || []);
    const uniqueTools = Array.from(new Set(tools));
    return [
      { value: 'all', label: 'Tools' },
      ...uniqueTools.map((tool) => ({ value: tool, label: tool })),
    ];
  }, [templates]);

  // Calculate status options
  const statusOptions = [
    { value: 'all', label: 'Status' },
    { value: 'ACTIVE', label: 'Active' },
    { value: 'DISABLED', label: 'Disabled' },
  ];

  // Calculate model options
  const modelOptions = useMemo(() => {
    const models = templates.map((t) => t.model);
    const uniqueModels = Array.from(new Set(models));
    return [
      { value: 'all', label: 'Models' },
      ...uniqueModels.map((model) => ({
        value: model,
        label: model,
      })),
    ];
  }, [templates]);

  // Menu options for each template row
  const MENU_OPTIONS = useCallback(
    (template: Template) => {
      const baseOptions = [
        {
          label: 'Edit',
          icon: 'solar:pen-bold',
          onClick: () => handleEditTemplate(template.id),
        },

        {
          label: 'Delete',
          icon: 'solar:trash-bin-trash-bold',
          onClick: () => handleOpenConfirmDialog(template.id),
          sx: { color: 'error.main' },
        },
      ];

      // Add publish status options for admin templates
      // if (activeTab !== 'admin') {
      //   baseOptions.splice(
      //     2,
      //     0,
      //     {
      //       label: 'Approve',
      //       icon: 'solar:check-circle-bold',
      //       onClick: () => handlePublishStatus(template.id, 'APPROVED'),
      //       sx: { color: 'success.main' },
      //     },
      //     {
      //       label: 'Reject',
      //       icon: 'solar:close-circle-bold',
      //       onClick: () => handlePublishStatus(template.id, 'REJECTED'),
      //       sx: { color: 'error.main' },
      //     }
      //   );
      // }
      if (activeTab === 'admin') {
        baseOptions.splice(2, 0, {
          label: template.status === 'ACTIVE' ? 'Disable' : 'Activate',
          icon: template.status === 'ACTIVE' ? 'solar:eye-closed-bold' : 'solar:eye-bold',
          onClick: () => handleToggleStatus(template),
          sx: {
            color: template.status === 'ACTIVE' ? 'warning.main' : 'success.main',
          },
        });
      }

      return baseOptions;
    },
    [
      handleEditTemplate,
      handleToggleStatus,
      handleOpenConfirmDialog,
      activeTab,
      handlePublishStatus,
    ]
  );

  // Table columns configuration
  const columns: AppTablePropsType<Template>['columns'] = useMemo(
    () => [
      {
        name: 'name',
        PreviewComponent: (data) => {
          const { name } = data;
          return (
            <Stack direction="row" alignItems="center" spacing={2}>
              <Box
                sx={{
                  width: 40,
                  height: 40,
                  borderRadius: '50%',
                  bgcolor: '#2C2C2C',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '14px',
                  fontWeight: 600,
                }}
              >
                {name.charAt(0).toUpperCase()}
              </Box>
              <Typography variant="body2" sx={{ fontWeight: 500, color: 'text.primary' }}>
                {name}
              </Typography>
            </Stack>
          );
        },
      },
      {
        name: 'createdAt',
        PreviewComponent: (data) => {
          const { createdAt } = data;
          const date = new Date(createdAt);
          const formattedDate = date.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
          });
          const formattedTime = date.toLocaleTimeString('en-GB', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
          });
          return (
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              {formattedDate} {formattedTime}
            </Typography>
          );
        },
      },
      {
        name: 'type',
        PreviewComponent: (data) => {
          const { type } = data;
          return (
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              {type === 'SINGLE' ? 'Single Agent' : "Team's Agent"}
            </Typography>
          );
        },
      },
      {
        name: 'category',
        PreviewComponent: (data) => {
          const { category } = data;
          return (
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              {category.name}
            </Typography>
          );
        },
      },
      {
        name: 'templateTools',
        PreviewComponent: (data) => {
          const { templateTools } = data;
          return (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Box
                sx={{
                  width: 20,
                  height: 20,
                  borderRadius: '4px',
                  bgcolor: '#8B4513',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Box
                  component="span"
                  sx={{
                    width: 12,
                    height: 12,
                    bgcolor: '#FF6B35',
                    borderRadius: '2px',
                  }}
                />
              </Box>
              {templateTools && templateTools.length > 1 && (
                <Typography variant="caption" sx={{ color: 'text.disabled', ml: 0.5 }}>
                  +{templateTools.length - 1}
                </Typography>
              )}
            </Box>
          );
        },
      },
      {
        name: 'model',
        PreviewComponent: (data) => {
          const { model } = data;
          let modelLabel = 'GPT-4o';

          switch (model) {
            case 'GPT_4O_MINI':
              modelLabel = 'GPT-4o Mini';
              break;
            case 'GPT_4O':
              modelLabel = 'GPT-4o';
              break;
            case 'CLAUDE_3_7_SONNET':
              modelLabel = 'Claude 3.5 Sonnet';
              break;
            case 'GEMINI_2_0_FLASH':
              modelLabel = 'Gemini 2.0 Flash';
              break;
            case 'GEMINI_1_5_FLASH':
              modelLabel = 'Gemini 1.5 Flash';
              break;
            default:
              modelLabel = model || 'GPT-4o';
          }

          return (
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              {modelLabel}
            </Typography>
          );
        },
      },
      {
        name: activeTab === 'admin' ? 'status' : 'publishRequestStatus',
        PreviewComponent: (data) => {
          if (activeTab === 'admin') {
            // Show status for admin tab
            const { status } = data;
            const isActive = status === 'ACTIVE';
            return (
              <Box
                sx={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  px: 1.5,
                  py: 0.5,
                  borderRadius: '12px',
                  bgcolor: isActive ? '#E8F5E8' : '#FFE8E8',
                  color: isActive ? '#2E7D32' : '#D32F2F',
                  fontSize: '12px',
                  fontWeight: 500,
                }}
              >
                {isActive ? 'Activated' : 'Disabled'}
              </Box>
            );
          }

          // Show publishRequestStatus for users tab
          const { publishRequestStatus } = data;
          let bgcolor = '#F5F5F5';
          let color = '#666666';
          let label = 'Unknown';

          switch (publishRequestStatus) {
            case 'PENDING':
              bgcolor = '#FFF3CD';
              color = '#856404';
              label = 'Pending';
              break;
            case 'APPROVED':
              bgcolor = '#E8F5E8';
              color = '#2E7D32';
              label = 'Approved';
              break;
            case 'REJECTED':
              bgcolor = '#FFE8E8';
              color = '#D32F2F';
              label = 'Rejected';
              break;
            default:
              label = publishRequestStatus || 'Unknown';
          }

          return (
            <Box
              sx={{
                display: 'inline-flex',
                alignItems: 'center',
                px: 1.5,
                py: 0.5,
                borderRadius: '12px',
                bgcolor,
                color,
                fontSize: '12px',
                fontWeight: 500,
              }}
            >
              {label}
            </Box>
          );
        },
      },
      {
        name: 'id',
        cellSx: { width: '50px', textAlign: 'center' },
        PreviewComponent: (row) => {
          return <LongMenu options={MENU_OPTIONS(row)} />;
        },
      },
    ],
    [MENU_OPTIONS]
  );

  // Tab configuration
  const tabs: TabConfig[] = [
    { value: 'admin', label: t('components.agents.adminTemplates') },
    { value: 'user', label: t('components.agents.usersTemplates') },
  ];

  return {
    // State
    openConfirmDialog,
    selectedId,
    table,
    templates: filteredTemplates,
    loading,
    error,
    isPending,
    searchQuery,
    categoryFilter,
    categoryOptions,
    typeFilter,
    typeOptions,
    toolFilter,
    toolOptions,
    modelFilter,
    modelOptions,
    statusFilter,
    statusOptions,

    // Tab state
    activeTab,
    tabs,

    // Handlers
    handleEditTemplate,
    handleOpenConfirmDialog,
    handleCloseConfirmDialog,
    handleConfirmDelete,
    handleSearchChange,
    handleCategoryChange,
    handleTypeChange,
    handleToolChange,
    handleModelChange,
    handleStatusChange,
    handleTabChange,

    // Table configuration
    MENU_OPTIONS,
    columns,

    // API
    refetch,
  };
};
