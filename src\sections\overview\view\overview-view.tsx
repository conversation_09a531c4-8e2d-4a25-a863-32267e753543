import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Alert from '@mui/material/Alert';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import { alpha, useTheme } from '@mui/material/styles';

import { DashboardContent } from 'src/layouts/dashboard';
import { Iconify } from 'src/components/iconify';
import { useMockedUser } from 'src/auth/hooks';
import { AppButton } from 'src/components/common';

// ----------------------------------------------------------------------

export function OverviewView() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { user } = useMockedUser();
  const [showAlert, setShowAlert] = useState(true);

  const handleResendEmail = () => {
    // Handle resend email logic
    console.log('Resending verification email...');
  };

  const handleCloseAlert = () => {
    setShowAlert(false);
  };

  // Mock data for statistics
  const stats = [
    {
      title: 'Total Users',
      subtitle: 'We daily update these statistics',
      items: [
        { label: 'Users', value: '15,689', icon: 'mdi:account-outline', color: '#7D40D9' },
        { label: 'Managers', value: '8', icon: 'fa6-regular:chess-queen', color: '#7D40D9' },
        { label: 'Admin', value: '1', icon: 'material-symbols:diamond-outline', color: '#7D40D9' },
      ],
    },
  ];

  const metrics = [
    {
      value: '49,795',
      label: 'Operations',
      subtitle: 'Total operations by agents',
      icon: 'material-symbols:check-box-outline',
      color: '#7D40D9',
      bgColor: alpha('#7D40D9', 0.1),
    },
    {
      value: '$16,894',
      label: 'Earnings',
      subtitle: 'The platform revenue',
      icon: 'material-symbols:payments-outline',
      color: '#7D40D9',
      bgColor: alpha('#7D40D9', 0.1),
    },
    {
      value: '1,356',
      label: 'Website visits last month',
      subtitle: '',
      icon: 'material-symbols:trending-up',
      color: '#7D40D9',
      bgColor: alpha('#7D40D9', 0.1),
    },
    {
      value: '92%',
      label: 'Task success completion rate',
      subtitle: '',
      icon: 'material-symbols:percent',
      color: '#7D40D9',
      bgColor: alpha('#7D40D9', 0.1),
    },
  ];

  return (
    <DashboardContent maxWidth="xl">
      {/* Header Section */}
      <Box
        component="img"
        src={
          theme.palette.mode === 'dark'
            ? '/assets/background/background-overview-dark.png'
            : '/assets/background/background-overview.png'
        }
        width="100%"
        height="100%"
      />

      {/* Verification Alert */}
      {showAlert && (
        <Alert
          severity="warning"
          sx={{
            mb: 3,
            p: 3,
            borderRadius: 2,
            '& .MuiAlert-message': {
              width: '100%',
            },
          }}
          action={
            <Stack direction="row" spacing={1}>
              <AppButton
                onClick={handleResendEmail}
                sx={{ background: 'white', color: 'black', width: '200px' }}
                label="Resend email"
              />

              <IconButton
                aria-label="close"
                color="inherit"
                size="small"
                onClick={handleCloseAlert}
              >
                <Iconify icon="eva:close-fill" />
              </IconButton>
            </Stack>
          }
        >
          We have sent a verification <NAME_EMAIL> to finish the registration process
          for your account.
        </Alert>
      )}

      {/* Greeting */}
      <Typography variant="h5" sx={{ mb: 3, fontWeight: 500 }}>
        Hello, <span style={{ color: '#7D40D9', fontWeight: 700, fontSize: '27px' }}>Adam</span>
      </Typography>

      {/* Statistics Cards */}
      <Grid container spacing={3}>
        {/* Total Users Section */}
        <Grid item xs={12}>
          <Card
            sx={{
              p: 4,
              borderRadius: 3,
              background: 'linear-gradient(135deg, #FAFBFC 0%, #F8F9FA 100%)',
              border: '1px solid #E5E7EB',
            }}
          >
            <Typography variant="h3" sx={{ mb: 1, fontWeight: 700, fontSize: 'bold' }}>
              Total Users
            </Typography>
            <Typography variant="body2" sx={{ mb: 4, fontWeight: 700, fontSize: 'bold' }}>
              We daily update these statistics
            </Typography>

            <Stack
              direction={{ xs: 'column', sm: 'row' }}
              spacing={0}
              divider={
                <Box
                  sx={{
                    width: { xs: '100%', sm: '1px' },
                    height: { xs: '1px', sm: '80px' },
                    bgcolor: '#E5E7EB',
                    mx: { xs: 0, sm: 3 },
                    my: { xs: 2, sm: 0 },
                  }}
                />
              }
            >
              {stats[0].items.map((item, index) => (
                <Box key={index} sx={{ flex: 1 }}>
                  <Stack alignItems="center" spacing={2} sx={{ py: 2 }}>
                    <Box
                      sx={{
                        width: 64,
                        height: 64,
                        borderRadius: 3,
                        bgcolor: alpha(item.color, 0.1),
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        boxShadow: '0 4px 12px rgba(125, 64, 217, 0.15)',
                      }}
                    >
                      <Iconify icon={item.icon} sx={{ width: 32, height: 32, color: item.color }} />
                    </Box>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography
                        variant="h3"
                        sx={{ fontWeight: 700, color: 'text.primary', mb: 0.5 }}
                      >
                        {item.value}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                        {item.label}
                      </Typography>
                    </Box>
                  </Stack>
                </Box>
              ))}
            </Stack>
          </Card>
        </Grid>

        {/* Metrics Cards */}
        {metrics.map((metric, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card
              sx={{
                p: 3,
                borderRadius: 3,
                height: '100%',
                background: 'linear-gradient(135deg, #FAFBFC 0%, #F8F9FA 100%)',
                border: '1px solid #E5E7EB',
                position: 'relative',
                '&::after':
                  index < metrics.length - 1
                    ? {
                        content: '""',
                        position: 'absolute',
                        top: '20%',
                        right: 0,
                        width: '1px',
                        height: '60%',
                        bgcolor: '#E5E7EB',
                        display: { xs: 'none', md: 'block' },
                      }
                    : {},
              }}
            >
              <Stack spacing={2}>
                <Box
                  sx={{
                    width: 56,
                    height: 56,
                    borderRadius: 3,
                    bgcolor: metric.bgColor,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: '0 4px 12px rgba(125, 64, 217, 0.15)',
                  }}
                >
                  <Iconify icon={metric.icon} sx={{ width: 28, height: 28, color: metric.color }} />
                </Box>

                <Box>
                  <Typography variant="h3" sx={{ fontWeight: 700, color: 'text.primary', mb: 0.5 }}>
                    {metric.value}
                  </Typography>
                  <Typography variant="body2" sx={{ fontWeight: 500, color: 'text.secondary' }}>
                    {metric.label}
                  </Typography>
                  {metric.subtitle && (
                    <Typography
                      variant="caption"
                      color="text.disabled"
                      sx={{ mt: 0.5, display: 'block' }}
                    >
                      {metric.subtitle}
                    </Typography>
                  )}
                </Box>
              </Stack>
            </Card>
          </Grid>
        ))}
      </Grid>
    </DashboardContent>
  );
}
