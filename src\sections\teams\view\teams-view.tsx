import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>ack,
  Typo<PERSON>,
  IconButton,
  Menu,
  MenuItem,
  Box,
  CircularProgress,
  Chip,
} from '@mui/material';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Iconify } from 'src/components/iconify';
import { paths } from 'src/routes/paths';
import { Label } from 'src/components/label';
import { AppButton, AppContainer } from 'src/components/common';
import { fDate } from 'src/utils/format-time';
import ConfirmDialog from 'src/components/custom-dialog/confirm-dialog';
import { useTeamsView, TemplateTeam } from './use-teams-view';
import TeamForm from '../form/team-form';

// ----------------------------------------------------------------------

// Get color based on team type
const getTeamTypeColor = (type: 'AUTO' | 'MANUAL') => {
  switch (type) {
    case 'AUTO':
      return 'success';
    case 'MANUAL':
      return 'warning';
    default:
      return 'default';
  }
};

export const TeamsView = () => {
  const { t } = useTranslation();

  // Menu state
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [menuTeamId, setMenuTeamId] = useState<number | null>(null);
  const openMenu = Boolean(menuAnchorEl);

  // Use the custom hook to manage teams
  const {
    teams,
    selectedTeam,
    openConfirmDialog,
    formDialog,
    loading,
    error,
    isPending,
    handleAddTeam,
    handleEditTeam,
    handleViewTeam,
    handleOpenConfirmDialog,
    handleCloseConfirmDialog,
    handleDeleteTeam,
    handleFormSubmit,
  } = useTeamsView();

  // Handle opening the menu
  const handleOpenMenu = (event: React.MouseEvent<HTMLButtonElement>, id: number) => {
    setMenuAnchorEl(event.currentTarget);
    setMenuTeamId(id);
  };

  // Handle closing the menu
  const handleCloseMenu = () => {
    setMenuAnchorEl(null);
    setMenuTeamId(null);
  };

  return (
    <AppContainer
      title={t('components.teams.title')}
      routeLinks={[
        { name: t('components.dashboard.overView'), href: paths.dashboard.root },
        { name: t('components.teams.title') },
      ]}
      buttons={[
        {
          label: t('components.buttons.createNewTeam'),
          variant: 'outlined',
          startIcon: <Iconify icon="eva:plus-fill" />,
          onClick: handleAddTeam,
        },
      ]}
    >
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 10 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Box sx={{ py: 10, textAlign: 'center' }}>
          <Typography variant="h6" color="error" paragraph>
            {error}
          </Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            Please try again later
          </Typography>
        </Box>
      ) : (
        <Grid container spacing={3}>
          {teams.map((team) => (
            <Grid item xs={12} sm={6} md={4} key={team.id}>
              <Card sx={{ p: 3, minHeight: '320px' }}>
                <Stack spacing={2}>
                  {/* Card Header - Category and Menu */}
                  <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Iconify
                        icon={team.category.icon}
                        sx={{
                          color: team.category.theme,
                          width: 24,
                          height: 24,
                        }}
                      />
                      <Label
                        variant="soft"
                        sx={{
                          bgcolor: `${team.category.theme}20`,
                          color: team.category.theme,
                          fontWeight: 'medium',
                        }}
                      >
                        {team.category.name}
                      </Label>
                    </Box>

                    <IconButton
                      size="small"
                      onClick={(event) => handleOpenMenu(event, team.id)}
                      sx={{ ml: 1 }}
                    >
                      <Iconify icon="eva:more-vertical-fill" />
                    </IconButton>
                  </Stack>

                  {/* Team Name and Type */}
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <Typography variant="h6" noWrap>
                      {team.name || `Team ${team.id}`}
                    </Typography>
                    <Label variant="soft" color={getTeamTypeColor(team.type)}>
                      {team.type}
                    </Label>
                  </Stack>

                  {/* Team Description */}
                  <Typography variant="body2" sx={{ color: 'text.secondary', flexGrow: 1 }}>
                    {team.description}
                  </Typography>

                  {/* Templates in Team */}
                  <Box>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      Templates ({team.templatesInTeam.length})
                    </Typography>
                    <Stack direction="row" spacing={1} sx={{ flexWrap: 'wrap', gap: 0.5 }}>
                      {team.templatesInTeam.slice(0, 3).map((templateInTeam) => (
                        <Chip
                          key={templateInTeam.id}
                          label={templateInTeam.template.name}
                          size="small"
                          variant="outlined"
                          sx={{ fontSize: '0.75rem' }}
                        />
                      ))}
                      {team.templatesInTeam.length > 3 && (
                        <Chip
                          label={`+${team.templatesInTeam.length - 3} more`}
                          size="small"
                          variant="outlined"
                          sx={{ fontSize: '0.75rem' }}
                        />
                      )}
                    </Stack>
                  </Box>

                  {/* Team Info */}
                  <Stack direction="row" alignItems="center" spacing={1} sx={{ pt: 1 }}>
                    <Typography variant="caption" sx={{ color: 'text.disabled' }}>
                      Model: {team.model.replace('_', ' ')}
                    </Typography>
                    <Box sx={{ flexGrow: 1 }} />
                    <Typography variant="caption" sx={{ color: 'text.disabled' }}>
                      {t('components.common.created')} {fDate(team.createdAt)}
                    </Typography>
                  </Stack>
                </Stack>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Team Form Dialog */}
      {formDialog.value && (
        <TeamForm
          open={formDialog.value}
          onClose={formDialog.onFalse}
          team={selectedTeam}
          onSubmit={handleFormSubmit}
        />
      )}

      {/* Actions Menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={openMenu}
        onClose={handleCloseMenu}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <MenuItem
          onClick={() => {
            if (menuTeamId) handleViewTeam(menuTeamId);
            handleCloseMenu();
          }}
        >
          <Iconify icon="eva:eye-fill" sx={{ mr: 2, width: 20, height: 20 }} />
          {t('components.common.view')}
        </MenuItem>
        <MenuItem
          onClick={() => {
            if (menuTeamId) handleEditTeam(menuTeamId);
            handleCloseMenu();
          }}
        >
          <Iconify icon="eva:edit-fill" sx={{ mr: 2, width: 20, height: 20 }} />
          {t('components.common.edit')}
        </MenuItem>
        <MenuItem
          onClick={() => {
            if (menuTeamId) handleOpenConfirmDialog(menuTeamId);
            handleCloseMenu();
          }}
          sx={{ color: 'error.main' }}
        >
          <Iconify icon="eva:trash-2-outline" sx={{ mr: 2, width: 20, height: 20 }} />
          {t('components.buttons.delete')}
        </MenuItem>
      </Menu>

      {/* Confirm Delete Dialog */}
      <ConfirmDialog
        open={openConfirmDialog}
        onClose={handleCloseConfirmDialog}
        close={handleCloseConfirmDialog}
        title={
          <Typography variant="h3" textAlign="center">
            {t('components.dialogs.deleteTeam')}
          </Typography>
        }
        content={
          <Typography variant="body1">{t('components.dialogs.deleteTeamConfirm')}</Typography>
        }
        icon={
          <Box sx={{ textAlign: 'center' }}>
            <Iconify
              icon="eva:alert-triangle-fill"
              sx={{ width: 64, height: 64, color: 'error.main' }}
            />
          </Box>
        }
        action={
          <Stack direction="row" justifyContent="center" spacing={1} sx={{ width: '100%' }}>
            <AppButton
              sx={{ width: '45%' }}
              label={t('components.buttons.delete')}
              variant="contained"
              color="error"
              isLoading={isPending}
              onClick={handleDeleteTeam}
            />
            <AppButton
              sx={{ width: '45%' }}
              label={t('components.buttons.cancel')}
              variant="outlined"
              color="inherit"
              onClick={handleCloseConfirmDialog}
            />
          </Stack>
        }
      />
    </AppContainer>
  );
};
