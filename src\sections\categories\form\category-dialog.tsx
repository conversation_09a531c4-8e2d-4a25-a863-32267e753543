import { Dialog, DialogContent, IconButton, Typography, Box, Stack } from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { Category } from 'src/services/api/use-categories-api';
import CategoryForm from './category-form';
import { CategoryFormValues } from './category-schema';

interface CategoryDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: CategoryFormValues) => void;
  defaultValues?: Partial<Category>;
  loading?: boolean;
}

export default function CategoryDialog({
  open,
  onClose,
  onSubmit,
  defaultValues,
  loading = false,
}: CategoryDialogProps) {
  const renderHead = (
    <Stack spacing={1.5} sx={{ mb: 5 }}>
      <Typography variant="h3" textAlign="center">
        {defaultValues ? 'Edit Category' : 'Add New Category'}
      </Typography>

      <Typography variant="body2" sx={{ color: 'text.secondary' }} textAlign="center">
        Please type the category name with description below
      </Typography>
    </Stack>
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          width: '100%',
          maxWidth: 712,
          borderRadius: 2,
          px: 4,
        },
      }}
    >
      <Box sx={{ position: 'relative', p: 3 }}>
        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'text.secondary',
          }}
        >
          <Iconify icon="eva:close-fill" width={24} height={24} />
        </IconButton>

        {renderHead}

        <DialogContent sx={{ p: 0 }}>
          <CategoryForm
            onSubmit={onSubmit}
            onCancel={onClose}
            defaultValues={defaultValues}
            loading={loading}
          />
        </DialogContent>
      </Box>
    </Dialog>
  );
}
