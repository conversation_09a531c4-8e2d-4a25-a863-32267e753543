import { Checkbox, TableCell, TableRow } from '@mui/material';
import { AppTableRowProps } from '../types/app-table-row';

const AppTableRow = <TRow,>({ columns, data, selectRow, index }: AppTableRowProps<TRow>) => {
  const columnsView = columns.map((column) => {
    if (!column) {
      return <TableCell key={index} />;
    }
    const PreviewComponent = column.PreviewComponent; // Specify type as React component
    return (
      <TableCell
        key={column.name}
        sx={{
          whiteSpace: 'nowrap',
          textAlign: 'left',
          verticalAlign: 'middle',
          ...column.cellSx
        }}
      >
        {PreviewComponent ? PreviewComponent(data, index) : nestedData(data, column.name)}
        {/* Access data using the column key */}
      </TableCell>
    );
  });

  return (
    <TableRow
      hover
      sx={{
        '& .MuiTableCell-root': {
          borderBottom: '1px solid',
          borderColor: 'divider',
          py: 2,
          px: 2,
        },
        '&:hover': {
          backgroundColor: 'action.hover',
        },
      }}
    >
      {selectRow?.handleSelectRow && (
        <TableCell padding="checkbox">
          <Checkbox
            checked={selectRow?.table.selected.includes(selectRow?.id)}
            onClick={() => selectRow?.handleSelectRow(data, selectRow.table)}
          />
        </TableCell>
      )}
      {columnsView}
    </TableRow>
  );
};

// Function to access nested data
export function nestedData(obj: any, path: string) {
  const keys = path.split('.');
  return keys.reduce((acc, key) => acc && acc[key], obj);
}

export default AppTableRow;
