// @mui
import { Checkbox } from '@mui/material';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { TableHeadPropsType } from './app-table/types/table-head-custom';
// ----------------------------------------------------------------------

export default function TableHeadCustom<T>({
  headLabel,
  sx,
  selectable,
}: TableHeadPropsType<T>) {
  return (
    <TableHead
      sx={{
        '& .MuiTableCell-head': {
          backgroundColor: 'transparent',
          color: 'text.secondary',
          fontWeight: 500,
          fontSize: '14px',
          borderBottom: '1px solid',
          borderColor: 'divider',
          py: 2,
        },
        ...sx,
      }}
    >
      <TableRow>
        {selectable?.handleSelectAllRows && (
          <TableCell padding="checkbox">
            <Checkbox
              indeterminate={
                !!selectable?.numSelected &&
                selectable?.numSelected < selectable?.rowCount!
              }
              checked={
                !!selectable?.rowCount &&
                selectable?.numSelected === selectable?.rowCount
              }
              onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                selectable.handleSelectAllRows(event.target.checked);
              }}
            />
          </TableCell>
        )}

        {headLabel.map((headCell) => (
          <TableCell
            key={headCell.id}
            align={(headCell.align as 'left') || 'left'}
            sx={{ width: headCell.width, minWidth: headCell.minWidth }}
          >
            {headCell.label}
          </TableCell>
        ))}
      </TableRow>
    </TableHead>
  );
}
