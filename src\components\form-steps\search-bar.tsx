import { TextField, InputAdornment } from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { ChangeEvent } from 'react';

// ----------------------------------------------------------------------

interface SearchBarProps {
  query: string;
  onChange: (event: ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  sx?: object;
}

export function SearchBar({ query, onChange, placeholder = 'Search...', sx }: SearchBarProps) {
  return (
    <TextField
      fullWidth
      value={query}
      onChange={onChange}
      placeholder={placeholder}
      InputProps={{
        startAdornment: (
          <InputAdornment position="start">
            <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
          </InputAdornment>
        ),
      }}
      sx={{
        '& .MuiOutlinedInput-root': {
          borderRadius: 2,
          bgcolor: 'background.neutral',
          '& fieldset': {
            borderColor: 'transparent',
          },
          '&:hover fieldset': {
            borderColor: 'primary.main',
          },
          '&.Mui-focused fieldset': {
            borderColor: 'primary.main',
          },
        },
        ...sx,
      }}
    />
  );
}

export default SearchBar;
