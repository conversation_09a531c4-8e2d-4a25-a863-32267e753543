import { UseMutationOptions, useMutation } from '@tanstack/react-query';
import { AxiosError, AxiosInstance } from 'axios';
import { useApiResult } from './use-api-result';

interface IUploadFileServiceParams {
  // Endpoint to get the S3 upload URL (GET request)
  presignedUrlEndpoint: string;
  // The S3 object key/path
  key: string;
  // Additional parameters for the GET request
  params?: Record<string, any>;
  onSuccess?: (fileUrl: string) => void;
  withSnackbar?: boolean;
  queryOptions?: UseMutationOptions<
    string, // Return type (file URL)
    AxiosError,
    File, // Input type (File object)
    string[]
  >;
}

export const useS3UploadService = ({ axiosInstance }: { axiosInstance: AxiosInstance }) => {
  const { handleApiSuccessWithSnackbar, handleApiErrorWithSnackbar } = useApiResult();

  const useUploadFile = ({
    presignedUrlEndpoint,
    key,
    params = {},
    onSuccess,
    withSnackbar = true,
    queryOptions,
  }: IUploadFileServiceParams) => {
    return useMutation<string, AxiosError, File, string[]>({
      mutationFn: async (file) => {
        // Step 1: GET request to obtain the S3 upload URL
        const presignedUrlResponse = await axiosInstance.get(presignedUrlEndpoint, {
          params: {
            key,
            ...params,
          },
        });

        const { url: s3UploadUrl } = presignedUrlResponse.data;

        // Step 2: PUT request directly to S3
        await axiosInstance.put(s3UploadUrl, file, {
          headers: {
            'Content-Type': file.type,
          },
          transformRequest: (data) => data, // Bypass axios serialization
        });

        // Return the public URL (assuming it's the same as the upload URL without query params)
        return s3UploadUrl.split('?')[0];
      },
      onSuccess: (fileUrl) => {
        if (withSnackbar) {
          handleApiSuccessWithSnackbar();
        }
        if (onSuccess) {
          onSuccess(fileUrl);
        }
      },
      onError: (error) => {
        handleApiErrorWithSnackbar(error);
      },
      ...queryOptions,
    });
  };

  return { useUploadFile };
};

//   const { useUploadFile } = useS3UploadService({ axiosInstance: yourAxiosInstance });

//   const { mutate: uploadFile, isPending } = useUploadFile({
//     presignedUrlEndpoint: '/api/files/download-url',
//     key: 'workforces/user-uploads/document.pdf', // The full S3 key/path
//     onSuccess: (fileUrl) => {
//       console.log('File available at:', fileUrl);
//     },
//   });

//   const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//     const file = e.target.files?.[0];
//     if (file) {
//       uploadFile(file);
//     }
//   };

//   return (
//     <div>
//       <input type="file" onChange={handleFileChange} disabled={isPending} />
//       {isPending && <p>Uploading...</p>}
//     </div>
//   );
// }
