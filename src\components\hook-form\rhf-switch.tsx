import { Switch, FormControlLabel, FormControlLabelProps, Typography } from '@mui/material';
import { useForm<PERSON>ontext, Controller } from 'react-hook-form';

// ----------------------------------------------------------------------

interface RHFSwitchProps extends Omit<FormControlLabelProps, 'control' | 'label'> {
  name: string;
  options: { value: string; label: string }[];
}

export default function RHFSwitch({ name, options, labelPlacement, disabled, ...other }: RHFSwitchProps) {
  const { control, watch } = useFormContext();
  const currentValue = watch(name);

  return (
    <FormControlLabel
      control={
        <Controller
          name={name}
          control={control}
          render={({ field }) => (
            <>
              <Typography variant='body2'>
                {options[1].label}
              </Typography>
              <Switch
                {...field}
                checked={currentValue === options[0].value}
                onChange={(event) => {
                  field.onChange(event.target.checked ? options[0].value : options[1].value);
                }}
                sx={{
                  ...(disabled && {
                    opacity: 0.5,
                    pointerEvents: 'none'
                  })
                }}
              />
            </>
          )}
        />
      }
      label={options[0].label}
      labelPlacement={labelPlacement}
      {...other}
    />
  );
}