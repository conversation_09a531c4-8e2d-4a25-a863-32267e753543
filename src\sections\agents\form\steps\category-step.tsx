import { useState, useMemo } from 'react';
import { Stack, Typography, Box } from '@mui/material';
import { Field } from 'src/components/hook-form/fields';
import { useCategoriesApi } from 'src/services/api/use-categories-api';
import ServiceSearchBar from '../components/service-search-bar';

// ----------------------------------------------------------------------

interface CategoryStepProps {
  // Add any specific props if needed
}

export function CategoryStep(_props: CategoryStepProps) {
  const [searchQuery, setSearchQuery] = useState('');

  // Get categories from API
  const { useGetCategories } = useCategoriesApi();
  const { data: categoriesResponse } = useGetCategories();
  const categories = categoriesResponse?.categories || [];

  // Filter categories based on search
  const filteredCategories = useMemo(() => {
    if (!searchQuery) return categories;

    return categories.filter(category =>
      category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      category.description.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [categories, searchQuery]);

  // Transform categories for Field.RadioGroup
  const options = filteredCategories.map(category => ({
    value: category.id.toString(),
    label: category.name,
  }));

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  return (
    <Stack spacing={2}>
      <Typography color="rgba(15, 14, 17, 0.65)" variant="h5">
        Select your agents template category
      </Typography>
      <Box bgcolor="white" p="20px" borderRadius="10px">
        <ServiceSearchBar
          query={searchQuery}
          onChange={handleSearchChange}
          placeholder="Search categories..."
        />

        <Field.RadioGroup
          name="category"
          options={options}
          sx={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: 1,
            '& .MuiFormControlLabel-root': {
              bgcolor: 'divider',
              width: '27.5vw',
              borderRadius: 1,
              p: 1,
              boxShadow: 1,
              display: 'flex',
              alignItems: 'center',
            },
            borderRadius: 1,
            p: 2,
          }}
        />
      </Box>
    </Stack>
  );
}

export default CategoryStep;
