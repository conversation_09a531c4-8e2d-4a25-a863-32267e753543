import { Stack, Typography } from '@mui/material';
import { Field } from 'src/components/hook-form/fields';
import { TYPE_OPTIONS, STATUS_OPTIONS } from '../config/agent-form-config';

// ----------------------------------------------------------------------

interface DetailsStepProps {
  // Add any specific props if needed
}

export function DetailsStep(_props: DetailsStepProps) {
  return (
    <>
      <Typography color="rgba(15, 14, 17, 0.65)" variant="h5" sx={{ mb: '20px' }}>
        Add your agents template details
      </Typography>
      <Stack spacing={3} sx={{ backgroundColor: 'white', p: 5, borderRadius: '10px' }}>
        <Typography my="-10px">Agent Name</Typography>
        <Field.Text name="name" placeholder="Type your agent name" />

        <Typography my="-10px">Description</Typography>
        <Field.Text name="description" placeholder="Type your agent description" multiline />

        <Typography my="-10px">System Prompt</Typography>
        <Field.Text
          rows={4}
          name="systemMessage"
          placeholder="Enter system instructions for the agent"
          multiline
        />

        <Typography>Select Agent Type</Typography>
        <Field.RadioGroup row name="type" sx={{ px: 3 }} options={TYPE_OPTIONS} />

        <Typography>Agent Status</Typography>
        <Field.Switch
          sx={{ px: 5 }}
          name="status"
          options={STATUS_OPTIONS}
          labelPlacement="end"
          onBlur={() => console.log('Blur event')}
        />
      </Stack>
    </>
  );
}

export default DetailsStep;
