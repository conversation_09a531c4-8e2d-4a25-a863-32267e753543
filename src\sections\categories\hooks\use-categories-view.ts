import { useState, useCallback, useEffect } from 'react';
import { Theme, useTheme } from '@mui/material/styles';
import {
  useCategoriesApi,
  Category,
  CreateCategoryRequest,
  UpdateCategoryRequest,
} from 'src/services/api/use-categories-api';

import { CategoryFormValues } from '../form/category-schema';

// Helper functions to convert between form data and API data

type ColorOptionValue =
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'info'
  | 'error'
  | 'custom';

function getColorFromValue(value: ColorOptionValue, theme: Theme): string {
  switch (value) {
    case 'primary':
      return theme.palette.primary.main;
    case 'secondary':
      return theme.palette.secondary.main;
    case 'success':
      return theme.palette.success.main;
    case 'warning':
      return theme.palette.warning.main;
    case 'info':
      return theme.palette.info.main;
    case 'error':
      return theme.palette.error.main;
    case 'custom':
      return '#FF5733';
    default:
      return theme.palette.grey[500];
  }
}
const convertFormToApiRequest = (
  formData: CategoryFormValues,
  theme: Theme
): CreateCategoryRequest | UpdateCategoryRequest => {
  return {
    name: formData.name,
    description: formData.description,
    icon: formData.icon,
    theme:
      formData.colorType === 'custom'
        ? formData.customColor || '#7D40D9'
        : getColorFromValue(formData.colorType, theme),
  };
};

const convertApiToFormData = (category: Category): CategoryFormValues => {
  // Check if theme is a hex color (custom) or a predefined color type
  const isCustomColor = category.theme.startsWith('#');

  return {
    name: category.name,
    description: category.description,
    icon: category.icon,
    colorType: isCustomColor ? 'custom' : (category.theme as any),
    customColor: isCustomColor ? category.theme : undefined,
  };
};

const convertApiCategoryToDisplayCategory = (apiCategory: Category): Category => {
  // Convert API category to display category with additional UI properties
  // Handle cases where theme might be undefined or null
  const theme = apiCategory.theme || '#7D40D9'; // Default to primary color if theme is missing
  const isCustomColor = theme.startsWith('#');

  return {
    ...apiCategory,
    theme,
    colorType: isCustomColor ? 'custom' : (theme as any),
    customColor: isCustomColor ? theme : undefined,
    agentsCount: apiCategory.agentsCount || 0,
  };
};

// This is a custom hook that combines the API services with local state management
export const useCategoriesView = () => {
  // State for categories
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const theme = useTheme();

  // Loading states for CRUD operations
  // const [isCreating, setIsCreating] = useState(false);
  // const [isUpdating, setIsUpdating] = useState(false);
  // const [isDeleting, setIsDeleting] = useState(false);

  // Get the API hooks
  const { useGetCategories, useCreateCategory, useUpdateCategory, useDeleteCategory } =
    useCategoriesApi();

  // API hooks with proper loading states and refetch
  const { mutate: createCategory, isPending: isCreating } = useCreateCategory();

  const { mutate: updateCategory, isPending: isUpdating } = useUpdateCategory(
    selectedCategory?.id || 0
  );

  const { mutate: deleteCategory, isPending: isDeleting } = useDeleteCategory();

  // Get categories data from the API
  const { data: categoriesResponse, isLoading, isError, refetch } = useGetCategories();

  // Update local state when API data changes
  useEffect(() => {
    if (categoriesResponse?.categories) {
      // Convert API categories to display categories
      const displayCategories = categoriesResponse.categories.map(
        convertApiCategoryToDisplayCategory
      );
      console.log('displayCategories ', displayCategories);
      setCategories(displayCategories);
      setLoading(false);
      setError(null);
    }
  }, [categoriesResponse]);

  // Update loading state
  useEffect(() => {
    setLoading(isLoading);
  }, [isLoading]);

  // Update error state
  useEffect(() => {
    if (isError) {
      setError('Failed to fetch categories');
    }
  }, [isError]);

  // Fetch categories function for manual refetching
  const fetchCategories = useCallback(() => {
    setLoading(true);
    refetch();
  }, [refetch]);

  // Initial fetch
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  // Dialog handlers
  const handleOpenDialog = useCallback((category?: Category) => {
    setSelectedCategory(category || null);
    setOpenDialog(true);
  }, []);

  const handleCloseDialog = useCallback(() => {
    setOpenDialog(false);
    setSelectedCategory(null);
  }, []);

  // Open the dialog to create a new category
  const handleOpenCreateDialog = useCallback(() => {
    setSelectedCategory(null);
    setOpenDialog(true);
  }, []);

  // Handle creating a new category
  const handleCreateCategory = useCallback(
    (data: CategoryFormValues) => {
      // Set loading state

      // Convert form data to API request format
      const apiRequest = convertFormToApiRequest(data, theme);

      // Use the mutation from the API hook
      createCategory(apiRequest, {
        onSuccess: () => {
          // On success: refetch data from backend and close dialog
          refetch();
          handleCloseDialog();
        },
        onError: (err) => {
          console.error('Failed to create category:', err);
        },
      });
    },
    [categories, createCategory, handleCloseDialog]
  );

  // Handle updating a category
  const handleUpdateCategory = useCallback(
    (data: CategoryFormValues) => {
      if (!selectedCategory) return;

      // Set loading state

      // Convert form data to API request format
      const apiRequest = convertFormToApiRequest(data, theme);

      // Use the mutation from the API hook
      updateCategory(apiRequest, {
        onSuccess: () => {
          // On success: refetch data from backend and close dialog
          refetch();
          handleCloseDialog();
        },
        onError: (err) => {
          console.error('Failed to update category:', err);
        },
      });
    },
    [selectedCategory, updateCategory, handleCloseDialog, refetch]
  );

  // Handle deleting a category
  const handleDeleteCategory = useCallback(
    (id: number | string) => {
      const numericId = typeof id === 'string' ? parseInt(id, 10) : id;

      // Set loading state

      // Use the mutation from the API hook
      deleteCategory(numericId, {
        onSuccess: () => {
          // On success: refetch data from backend
          refetch();
        },
        onError: (err) => {
          console.error('Failed to delete category:', err);
        },
      });
    },
    [deleteCategory, refetch]
  );

  // Handle editing a category
  const handleEditCategory = useCallback(
    (id: number | string) => {
      const numericId = typeof id === 'string' ? parseInt(id, 10) : id;
      const category = categories.find((cat) => cat.id === numericId);
      if (category) {
        // Convert API category to form data for editing
        const formData = convertApiToFormData(category);
        handleOpenDialog({ ...category, ...formData } as Category);
      }
    },
    [categories, handleOpenDialog]
  );

  return {
    // State
    categories,
    loading,
    error,
    openDialog,
    selectedCategory,

    // Loading states for CRUD operations
    isLoading: isCreating || isUpdating,
    isDeleting,

    // Handlers
    handleOpenDialog,
    handleCloseDialog,
    handleCreateCategory,
    handleUpdateCategory,
    handleDeleteCategory,
    handleEditCategory,
    handleOpenCreateDialog,

    // Refetch
    refetch: fetchCategories,
  };
};
