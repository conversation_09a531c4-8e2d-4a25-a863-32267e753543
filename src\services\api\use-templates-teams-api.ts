import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for templates-teams
export const templatesTeamsEndpoints = {
  list: '/templates-teams',
  details: '/templates-teams',
};

// Define the TemplateInTeam data type
export interface TemplateInTeam {
  id: string;
  createdAt: string;
  template: {
    id: number;
    creatorId: number;
    name: string;
    description: string;
    categoryId: number;
    type: 'SINGLE' | 'MULTI';
    model: 'GPT_4O_MINI' | 'GPT_4O' | 'GPT_3_5_TURBO';
    createdAt: string;
    updatedAt: string;
    category: {
      id: number;
      name: string;
      description: string;
      icon: string;
      theme: string;
      createdAt: string;
      updatedAt: string;
    };
    systemMessage: string;
  };
}

// Define the TemplateTeam data type based on API response
export interface TemplateTeam {
  id: number;
  createdAt: string;
  updatedAt: string;
  name?: string; // Added for the POST request
  description: string;
  categoryId: number;
  type: 'AUTO' | 'MANUAL';
  model: 'GPT_4O_MINI' | 'GPT_4O' | 'CLAUDE_3_7_SONNET' | 'GEMINI_2_0_FLASH' | 'GEMINI_1_5_FLASH';
  category: {
    id: number;
    name: string;
    description: string;
    icon: string;
    theme: string;
    createdAt: string;
    updatedAt: string;
  };
  templatesInTeam: TemplateInTeam[];
}

// Define the API response types
export interface TemplatesTeamsListResponse {
  templatesTeams: TemplateTeam[];
  total: number;
}

// Define the request body types for creating a team
export interface CreateTemplateTeamRequest {
  name: string;
  type: 'AUTO' | 'MANUAL';
  categoryId: number;
  model: 'GPT_4O_MINI' | 'GPT_4O' | 'CLAUDE_3_7_SONNET' | 'GEMINI_2_0_FLASH' | 'GEMINI_1_5_FLASH';
  description: string;
  templatesIds: number[];
}

// Define the request body types for updating a team
export interface UpdateTemplateTeamRequest {
  name: string;
  type: 'AUTO' | 'MANUAL';
  categoryId: number;
  model: 'GPT_4O_MINI' | 'GPT_4O' | 'CLAUDE_3_7_SONNET' | 'GEMINI_2_0_FLASH' | 'GEMINI_1_5_FLASH';
  description: string;
  templatesIds: number[];
}

// Define query parameters for list endpoint
export interface TemplatesTeamsQueryParams {
  take?: number;
  skip?: number;
  name?: string;
  orderBy?: string;
  order?: 'asc' | 'desc';
}

// Define the available model options as constants
export const TEAM_MODEL_OPTIONS = [
  { value: 'GPT_4O_MINI', label: 'GPT-4o Mini' },
  { value: 'GPT_4O', label: 'GPT-4o' },
  { value: 'CLAUDE_3_7_SONNET', label: 'Claude 3.7 Sonnet' },
  { value: 'GEMINI_2_0_FLASH', label: 'Gemini 2.0 Flash' },
  { value: 'GEMINI_1_5_FLASH', label: 'Gemini 1.5 Flash' },
] as const;

// Define the available type options as constants
export const TEAM_TYPE_OPTIONS = [
  { value: 'AUTO', label: 'Auto' },
  { value: 'MANUAL', label: 'Manual' },
];

// Create a hook to use the templates-teams API
export const useTemplatesTeamsApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all templates-teams
  const useGetTemplatesTeams = (params?: TemplatesTeamsQueryParams) => {
    return apiServices.useGetListService<TemplatesTeamsListResponse, TemplatesTeamsQueryParams>({
      url: templatesTeamsEndpoints.list,
      params,
    });
  };

  // Get a single template-team by ID
  const useGetTemplateTeam = (id: number) => {
    return apiServices.useGetItemService<TemplateTeam>({
      url: templatesTeamsEndpoints.details,
      id: id.toString(),
    });
  };

  // Create a new template-team
  const useCreateTemplateTeam = (onSuccess?: (data: TemplateTeam) => void) => {
    return apiServices.usePostService<CreateTemplateTeamRequest, TemplateTeam>({
      url: templatesTeamsEndpoints.list,
      onSuccess,
      withFormData: false,
    });
  };

  // Update a template-team using PATCH
  const useUpdateTemplateTeam = (id: number, onSuccess?: () => void) => {
    return apiServices.usePatchService<UpdateTemplateTeamRequest>({
      url: templatesTeamsEndpoints.details,
      id: id.toString(),
      onSuccess,
      withFormData: false,
      queryKey: templatesTeamsEndpoints.list + 'list',
    });
  };

  // Delete a template-team
  const useDeleteTemplateTeam = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<number>({
      url: templatesTeamsEndpoints.details,
      urlAfterSuccess: templatesTeamsEndpoints.list + 'list',
      onSuccess,
    });
  };

  return {
    useGetTemplatesTeams,
    useGetTemplateTeam,
    useCreateTemplateTeam,
    useUpdateTemplateTeam,
    useDeleteTemplateTeam,
  };
};
