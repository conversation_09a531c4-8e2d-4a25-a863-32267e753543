import { Box, Stack, Typography, Paper } from '@mui/material';
import { ReactNode } from 'react';

// ----------------------------------------------------------------------

interface FormStepWrapperProps {
  title: string;
  description?: string;
  children: ReactNode;
  sx?: object;
}

export function FormStepWrapper({ title, description, children, sx }: FormStepWrapperProps) {
  return (
    <Stack spacing={3} sx={sx}>
      <Stack spacing={1}>
        <Typography variant="h5" sx={{ color: 'text.primary', fontWeight: 600 }}>
          {title}
        </Typography>
        {description && (
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            {description}
          </Typography>
        )}
      </Stack>
      
      <Paper
        elevation={0}
        sx={{
          p: 3,
          bgcolor: 'background.paper',
          border: '1px solid',
          borderColor: 'divider',
          borderRadius: 2,
        }}
      >
        {children}
      </Paper>
    </Stack>
  );
}

export default FormStepWrapper;
